# 产品搜索功能优化方案

## 1. 问题分析总结

当前产品搜索功能存在三大核心问题，导致搜索结果不准确、用户体验差。

### 问题1：MongoDB文本搜索索引配置不当
- **现象**: 搜索“芒果”时，返回不相关的“伊利大果粒”，而非“100%芒果复合果汁”。
- **根源**: 索引配置中 `default_language: 'none'`。此设置使用简单的空格作为分词器，无法正确处理中文词汇（如“芒果”被拆分为“芒”和“果”），导致匹配度计算错误。

### 问题2：搜索策略逻辑缺陷
- **现象**: 即便正则表达式（regex）能搜到正确结果，但由于文本搜索返回了不相关但非空的结果，导致regex搜索被完全跳过。
- **根源**: [`products-backend/src/routes/search.ts:58-78`](products-backend/src/routes/search.ts:58) 处的逻辑是“文本搜索优先，仅在无结果时回退到正则搜索”。这种瀑布流策略在文本搜索结果质量低时会屏蔽掉更精确的搜索方式。

### 问题3：缺少结果质量评估机制
- **现象**: 系统无法区分文本搜索返回的结果是高质量匹配还是低质量的偶然匹配。
- **根源**: 缺乏一个综合性的相关性评分机制。虽然文本搜索提供了 `textScore`，但没有结合其他业务规则（如精确匹配、字段权重）来综合评估结果的相关性。

---

## 2. 解决方案

为了系统性地解决上述问题，我们提出一个三管齐下的优化方案：**优化索引**、**改进策略**和**引入评分**。

### 方案一：优化MongoDB文本索引 (高优先级)

这是解决中文搜索问题的根本。

**具体步骤:**

1.  **修改索引配置**:
    在 [`products-backend/scripts/migrate-search-indexes.js:75`](products-backend/scripts/migrate-search-indexes.js:75) 文件中，将 `default_language` 从 `'none'` 修改为 `'simple'`。

    ```javascript
    // from
    default_language: 'none',
    
    // to
    default_language: 'simple',
    ```

    **理由**: `'simple'` 分词器虽然不完美，但它会根据Unicode标准进行分词，比 `'none'` 更适合处理不带空格的语言（如中文），能有效提升基础分词能力。

2.  **重新执行索引迁移脚本**:
    在后端项目中运行 `node scripts/migrate-search-indexes.js` 来删除旧索引并应用新的索引配置。

**长期优化建议**:
- 引入更专业的中文分词插件（如 `mongo-jieba`）或在应用层使用分词库（如 `node-jieba`）对搜索词进行预处理，然后将分词后的结果传递给MongoDB进行搜索。

### 方案二：改进搜索策略 (高优先级)

这是解决“劣币驱逐良币”问题的关键。我们需要从“瀑布模型”转向“混合并行模型”。

**具体步骤:**

1.  **并行执行两种搜索**:
    修改 [`products-backend/src/routes/search.ts`](products-backend/src/routes/search.ts) 的搜索逻辑。不再是依次执行，而是使用 `Promise.all` 并行执行 **文本搜索** 和 **正则表达式搜索**。

2.  **合并与去重**:
    - 将两个搜索的结果集合并。
    - 使用 `productId` 作为唯一标识符，对合并后的结果进行去重，避免重复展示。

3.  **保留来源信息**:
    在合并过程中，可以为每个产品对象临时附加一个标记，说明其来源（`text`, `regex`, or `both`），这对于后续的评分和调试非常有帮助。

### 方案三：实现搜索结果相关性评分机制 (中优先级)

这是提升搜索结果质量和排序合理性的核心。

**具体步骤:**

1.  **定义综合评分函数 `calculateRelevance`**:
    在 [`products-backend/src/routes/search.ts`](products-backend/src/routes/search.ts) 中创建一个函数，该函数接收一个产品对象和搜索词，返回一个综合评分。

2.  **设计评分规则**:
    评分可以由多个维度加权构成：
    - **基础分 (from `textScore`)**: 如果产品来自文本搜索，使用MongoDB返回的 `score` 作为基础分。来自正则搜索的可以给一个较低的默认基础分。
    - **精确匹配加分**: 如果 `product.name.display` 与搜索词完全相同，给予大量加分（例如 +100）。
    - **名称包含加分**: 如果 `product.name.display` 包含搜索词，给予中等加分（例如 +20）。
    - **字段权重加分**: 如果匹配发生在关键字段（如 `name`），加分高于次要字段（如 `specification`）。
    - **词序加分**: （高级）如果产品名称中的词序与搜索词一致，给予额外加分。

3.  **应用评分与排序**:
    - 在合并去重后，遍历所有结果，调用 `calculateRelevance` 计算每个产品的综合评分。
    - 最终的排序依据 `综合评分` (降序) 和 `collectTime` (降序) 作为次要排序标准。

---

## 3. 实施步骤与优先级

1.  **[P0 - 高] 改进搜索策略**:
    - **文件**: [`products-backend/src/routes/search.ts`](products-backend/src/routes/search.ts)
    - **任务**: 修改搜索逻辑为并行执行文本和正则搜索，并合并去重结果。
    - **影响**: 立竿见影。能立即召回之前被屏蔽的正确结果。

2.  **[P0 - 高] 优化MongoDB索引**:
    - **文件**: [`products-backend/scripts/migrate-search-indexes.js`](products-backend/scripts/migrate-search-indexes.js)
    - **任务**: 修改 `default_language` 为 `simple` 并执行脚本。
    - **影响**: 根本性提升中文搜索质量。

3.  **[P1 - 中] 实现相关性评分机制**:
    - **文件**: [`products-backend/src/routes/search.ts`](products-backend/src/routes/search.ts)
    - **任务**: 创建 `calculateRelevance` 函数，并根据新的评分对结果进行排序。
    - **影响**: 大幅提升搜索结果的排序质量和相关性。

4.  **[P2 - 低] 前端适应性调整**:
    - **文件**: [`product-showcase/src/hooks/useProducts.ts`](product-showcase/src/hooks/useProducts.ts) 及相关组件。
    - **任务**: 如果后端API响应有变动，或需要显示调试信息（如评分），前端需要做相应调整。目前看，如果API响应结构不变，前端可能无需改动。

通过以上步骤，我们可以系统性地解决当前搜索功能的所有已知问题，显著提升其准确性、相关性和用户体验。